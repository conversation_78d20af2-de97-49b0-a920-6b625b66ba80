"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/logs/page",{

/***/ "(app-pages-browser)/./src/utils/logFormatting.ts":
/*!************************************!*\
  !*** ./src/utils/logFormatting.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatModelName: () => (/* binding */ formatModelName),\n/* harmony export */   formatProviderName: () => (/* binding */ formatProviderName),\n/* harmony export */   formatRoleName: () => (/* binding */ formatRoleName),\n/* harmony export */   generateRoleUsedMessage: () => (/* binding */ generateRoleUsedMessage),\n/* harmony export */   getRoleUsedBadgeClass: () => (/* binding */ getRoleUsedBadgeClass),\n/* harmony export */   transformRoleUsed: () => (/* binding */ transformRoleUsed)\n/* harmony export */ });\n/**\n * Utility functions for transforming debug-style log messages into production-ready, user-friendly text\n */ /**\n * Check if a string looks like a role name (vs a technical debug pattern)\n */ const isLikelyRoleName = (str)=>{\n    // Exclude obvious technical patterns\n    const technicalPatterns = [\n        /default_key/i,\n        /attempt_\\d+/i,\n        /status_\\d+/i,\n        /failed/i,\n        /success/i,\n        /complexity_rr/i,\n        /fallback_position/i,\n        /^[a-f0-9-]{8,}/i,\n        /_then_/i,\n        /classification_/i,\n        /no_prompt/i,\n        /error/i\n    ];\n    // If it matches any technical pattern, it's not a role name\n    if (technicalPatterns.some((pattern)=>pattern.test(str))) {\n        return false;\n    }\n    // If it's a simple word or snake_case without numbers/technical terms, likely a role\n    return /^[a-z_]+$/i.test(str) && str.length > 2 && str.length < 50;\n};\n/**\n * Transform debug-style role_used messages into user-friendly text\n */ const transformRoleUsed = (roleUsed)=>{\n    if (!roleUsed) return {\n        text: 'N/A',\n        type: 'fallback'\n    };\n    // Handle simple role names first (clean role names without technical patterns)\n    if (isLikelyRoleName(roleUsed)) {\n        return {\n            text: formatRoleName(roleUsed),\n            type: 'role',\n            details: \"Role-based routing: \".concat(formatRoleName(roleUsed))\n        };\n    }\n    // Handle default key success patterns\n    if (roleUsed.includes('default_key') && roleUsed.includes('success')) {\n        const attemptMatch = roleUsed.match(/attempt_(\\d+)/);\n        const attempt = attemptMatch ? parseInt(attemptMatch[1]) : 1;\n        return {\n            text: attempt === 1 ? 'Default Key' : \"Default Key (Attempt \".concat(attempt, \")\"),\n            type: 'success',\n            details: attempt > 1 ? \"Required \".concat(attempt, \" attempts to succeed\") : undefined\n        };\n    }\n    // Handle default key failure patterns\n    if (roleUsed.includes('default_key') && roleUsed.includes('failed')) {\n        const attemptMatch = roleUsed.match(/attempt_(\\d+)/);\n        const statusMatch = roleUsed.match(/status_(\\w+)/);\n        const attempt = attemptMatch ? parseInt(attemptMatch[1]) : 1;\n        const status = statusMatch ? statusMatch[1] : 'unknown';\n        return {\n            text: \"Failed (Attempt \".concat(attempt, \")\"),\n            type: 'error',\n            details: \"Failed with status: \".concat(status)\n        };\n    }\n    // Handle multiple attempts failed\n    if (roleUsed.includes('default_all') && roleUsed.includes('attempts_failed')) {\n        const countMatch = roleUsed.match(/default_all_(\\d+)_attempts/);\n        const count = countMatch ? parseInt(countMatch[1]) : 0;\n        return {\n            text: \"All Keys Failed (\".concat(count, \" attempts)\"),\n            type: 'error',\n            details: \"Tried \".concat(count, \" different API keys, all failed\")\n        };\n    }\n    // Handle enhanced complexity-based routing with proximal search details\n    if (roleUsed.includes('complexity_rr_clsf_') || roleUsed.includes('complexity_level_')) {\n        // Enhanced pattern: complexity_rr_clsf_3_used_lvl_4_key_selected\n        const enhancedMatch = roleUsed.match(/complexity_rr_clsf_(\\d+)_used_lvl_(\\d+)/);\n        if (enhancedMatch) {\n            const classifiedLevel = enhancedMatch[1];\n            const usedLevel = enhancedMatch[2];\n            if (classifiedLevel === usedLevel) {\n                return {\n                    text: \"Complexity Level \".concat(usedLevel),\n                    type: 'success',\n                    details: \"Classified and routed to complexity level \".concat(usedLevel)\n                };\n            } else {\n                return {\n                    text: \"Complexity \".concat(classifiedLevel, \"→\").concat(usedLevel),\n                    type: 'success',\n                    details: \"Classified as level \".concat(classifiedLevel, \", routed to available level \").concat(usedLevel)\n                };\n            }\n        }\n        // Simple pattern: complexity_level_3\n        const levelMatch = roleUsed.match(/complexity_level_(\\d+)/);\n        if (levelMatch) {\n            const level = levelMatch[1];\n            return {\n                text: \"Complexity Level \".concat(level),\n                type: 'success',\n                details: \"Routed based on prompt complexity analysis\"\n            };\n        }\n    }\n    // Handle strict fallback\n    if (roleUsed.includes('fallback_position_')) {\n        const posMatch = roleUsed.match(/fallback_position_(\\d+)/);\n        const position = posMatch ? parseInt(posMatch[1]) : 0;\n        return {\n            text: \"Fallback Key #\".concat(position + 1),\n            type: 'success',\n            details: \"Used fallback key at position \".concat(position + 1)\n        };\n    }\n    // Handle intelligent role routing\n    if (roleUsed.includes('intelligent_role_')) {\n        const roleMatch = roleUsed.match(/intelligent_role_(.+)$/);\n        const detectedRole = roleMatch ? roleMatch[1] : 'unknown';\n        return {\n            text: \"Smart: \".concat(formatRoleName(detectedRole)),\n            type: 'role',\n            details: \"AI detected role: \".concat(formatRoleName(detectedRole))\n        };\n    }\n    // Enhanced fallback: Extract meaningful information from any unrecognized pattern\n    return extractMeaningfulInfo(roleUsed);\n};\n/**\n * Extract meaningful information from unrecognized role_used patterns\n */ const extractMeaningfulInfo = (roleUsed)=>{\n    // Try to extract complexity information\n    const complexityMatch = roleUsed.match(/complexity[_\\s]*(\\d+)/i);\n    if (complexityMatch) {\n        const level = complexityMatch[1];\n        return {\n            text: \"Complexity Level \".concat(level),\n            type: 'success',\n            details: \"Extracted complexity level \".concat(level, \" from routing pattern\")\n        };\n    }\n    // Try to extract role names from complex patterns\n    const roleNameMatch = extractRoleFromPattern(roleUsed);\n    if (roleNameMatch) {\n        return {\n            text: formatRoleName(roleNameMatch),\n            type: 'role',\n            details: \"Extracted role: \".concat(formatRoleName(roleNameMatch))\n        };\n    }\n    // Try to extract fallback information\n    const fallbackMatch = roleUsed.match(/fallback[_\\s]*(\\d+)/i);\n    if (fallbackMatch) {\n        const position = parseInt(fallbackMatch[1]);\n        return {\n            text: \"Fallback Key #\".concat(position + 1),\n            type: 'success',\n            details: \"Extracted fallback position \".concat(position + 1)\n        };\n    }\n    // Try to extract attempt information\n    const attemptMatch = roleUsed.match(/attempt[_\\s]*(\\d+)/i);\n    if (attemptMatch) {\n        const attempt = parseInt(attemptMatch[1]);\n        const isSuccess = roleUsed.toLowerCase().includes('success');\n        const isFailed = roleUsed.toLowerCase().includes('fail');\n        if (isSuccess) {\n            return {\n                text: attempt === 1 ? 'Default Key' : \"Default Key (Attempt \".concat(attempt, \")\"),\n                type: 'success',\n                details: \"Extracted success on attempt \".concat(attempt)\n            };\n        } else if (isFailed) {\n            return {\n                text: \"Failed (Attempt \".concat(attempt, \")\"),\n                type: 'error',\n                details: \"Extracted failure on attempt \".concat(attempt)\n            };\n        }\n    }\n    // Last resort: try to clean up the raw string for display\n    const cleanedText = cleanRawRoleUsed(roleUsed);\n    return {\n        text: cleanedText,\n        type: 'fallback',\n        details: \"Raw routing pattern: \".concat(roleUsed)\n    };\n};\n/**\n * Extract role names from complex patterns\n */ const extractRoleFromPattern = (str)=>{\n    // Look for patterns like \"classified_as_ROLENAME_something\"\n    const classifiedMatch = str.match(/classified_as_([a-z_]+)_/i);\n    if (classifiedMatch && isLikelyRoleName(classifiedMatch[1])) {\n        return classifiedMatch[1];\n    }\n    // Look for patterns like \"role_ROLENAME_something\"\n    const roleMatch = str.match(/role_([a-z_]+)_/i);\n    if (roleMatch && isLikelyRoleName(roleMatch[1])) {\n        return roleMatch[1];\n    }\n    // Look for patterns like \"fb_role_ROLENAME\"\n    const fbRoleMatch = str.match(/fb_role_([a-z_]+)/i);\n    if (fbRoleMatch && isLikelyRoleName(fbRoleMatch[1])) {\n        return fbRoleMatch[1];\n    }\n    return null;\n};\n/**\n * Clean up raw role_used strings for display as last resort\n */ const cleanRawRoleUsed = (str)=>{\n    // Remove common technical prefixes/suffixes\n    const cleaned = str.replace(/^default_key_[a-f0-9-]+_/i, '').replace(/_attempt_\\d+$/i, '').replace(/_status_\\w+$/i, '').replace(/_key_selected$/i, '').replace(/_then_.*$/i, '').replace(/^complexity_rr_/i, '').replace(/_no_.*$/i, '');\n    // If we cleaned it down to something reasonable, format it\n    if (cleaned && cleaned.length > 2 && cleaned.length < 30 && isLikelyRoleName(cleaned)) {\n        return formatRoleName(cleaned);\n    }\n    // Otherwise, just clean up the original string minimally\n    return str.replace(/_/g, ' ').replace(/([a-z])([A-Z])/g, '$1 $2').split(' ').map((word)=>word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(' ').substring(0, 30) + (str.length > 30 ? '...' : '');\n};\n/**\n * Convert snake_case role names to Title Case\n */ const formatRoleName = (roleName)=>{\n    return roleName.split('_').map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(' ');\n};\n/**\n * Get CSS classes for role used badges based on type\n */ const getRoleUsedBadgeClass = (type)=>{\n    switch(type){\n        case 'role':\n            return 'inline-block px-2 py-1 rounded text-xs font-medium bg-blue-500/15 text-blue-300 border border-blue-500/25';\n        case 'success':\n            return 'inline-block px-2 py-1 rounded text-xs font-medium bg-green-500/15 text-green-300 border border-green-500/25';\n        case 'error':\n            return 'inline-block px-2 py-1 rounded text-xs font-medium bg-red-500/15 text-red-300 border border-red-500/25';\n        case 'fallback':\n        default:\n            return 'inline-block px-2 py-1 rounded text-xs font-medium bg-orange-500/15 text-orange-300 border border-orange-500/25';\n    }\n};\n/**\n * Generate production-ready role_used strings for logging\n */ const generateRoleUsedMessage = {\n    // Default routing messages\n    defaultKeySuccess: function() {\n        let attempt = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        return attempt === 1 ? 'default_key_success' : \"default_key_success_attempt_\".concat(attempt);\n    },\n    defaultKeyFailed: function() {\n        let attempt = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, status = arguments.length > 1 ? arguments[1] : void 0;\n        return \"default_key_failed\".concat(status ? \"_status_\".concat(status) : '', \"_attempt_\").concat(attempt);\n    },\n    allKeysFailed: (attemptCount)=>\"default_all_\".concat(attemptCount, \"_attempts_failed\"),\n    // Role-based routing messages\n    roleRouting: (roleName)=>roleName,\n    intelligentRoleRouting: (detectedRole)=>\"intelligent_role_\".concat(detectedRole),\n    // Complexity-based routing messages\n    complexityRouting: (level, keyIndex)=>keyIndex !== undefined ? \"complexity_level_\".concat(level, \"_key_\").concat(keyIndex) : \"complexity_level_\".concat(level),\n    // Strict fallback routing messages\n    fallbackRouting: (position)=>\"fallback_position_\".concat(position),\n    // Error states\n    noKeysAvailable: ()=>'no_keys_available',\n    configurationError: ()=>'configuration_error',\n    routingStrategyError: (strategy)=>\"routing_strategy_error_\".concat(strategy)\n};\n/**\n * Transform provider names to user-friendly display names\n */ const formatProviderName = (provider)=>{\n    if (!provider) return 'N/A';\n    const providerMap = {\n        'openai': 'OpenAI',\n        'anthropic': 'Anthropic',\n        'google': 'Google',\n        'openrouter': 'OpenRouter',\n        'deepseek': 'DeepSeek',\n        'xai': 'xAI',\n        'langgraph': 'RouKey Orchestration',\n        'langgraph_orchestration': 'RouKey Orchestration',\n        'hybrid_orchestration': 'RouKey Orchestration',\n        'roukey': 'RouKey Orchestration'\n    };\n    return providerMap[provider.toLowerCase()] || provider;\n};\n/**\n * Transform model names to user-friendly display names\n */ const formatModelName = (modelName)=>{\n    if (!modelName) return 'N/A';\n    // Remove common prefixes and make more readable\n    return modelName.replace(/^(gpt-|claude-|gemini-|meta-llama\\/|deepseek-|grok-)/, '').replace(/-/g, ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase());\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/logFormatting.ts\n"));

/***/ })

});