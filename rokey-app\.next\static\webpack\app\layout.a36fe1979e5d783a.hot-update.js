"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c28d1a657505\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImMyOGQxYTY1NzUwNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AcademicCapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/AcademicCapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/ChartBarIcon.js\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useRoutePrefetch */ \"(app-pages-browser)/./src/hooks/useRoutePrefetch.ts\");\n/* harmony import */ var _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useChatHistory */ \"(app-pages-browser)/./src/hooks/useChatHistory.ts\");\n/* harmony import */ var _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/usePredictiveNavigation */ \"(app-pages-browser)/./src/hooks/usePredictiveNavigation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst navItems = [\n    {\n        href: \"/dashboard\",\n        label: \"Dashboard\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        description: \"Overview & analytics\"\n    },\n    {\n        href: \"/my-models\",\n        label: \"My Models\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        description: \"API key management\"\n    },\n    {\n        href: \"/playground\",\n        label: \"Playground\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        description: \"Test your models\"\n    },\n    {\n        href: \"/routing-setup\",\n        label: \"Routing Setup\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        description: \"Configure routing\"\n    },\n    // Manual Build temporarily hidden for launch\n    // {\n    //   href: \"/manual-build\",\n    //   label: \"Manual Build\",\n    //   icon: CubeIcon,\n    //   iconSolid: CubeIconSolid,\n    //   description: \"Visual workflow builder\"\n    // },\n    {\n        href: \"/logs\",\n        label: \"Logs\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        description: \"Request history\"\n    },\n    {\n        href: \"/training\",\n        label: \"Training\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n        description: \"AI training & knowledge\"\n    },\n    {\n        href: \"/analytics\",\n        label: \"Analytics\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n        description: \"Advanced insights\"\n    }\n];\nfunction Sidebar() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { isCollapsed, isHovered, isHoverDisabled, setHovered } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_5__.useSidebar)();\n    const navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_6__.useNavigationSafe)();\n    const { navigateOptimistically } = navigationContext || {\n        navigateOptimistically: ()=>{}\n    };\n    const { prefetchOnHover } = (0,_hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useRoutePrefetch)();\n    const { prefetchWhenIdle } = (0,_hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useIntelligentPrefetch)();\n    const { prefetchChatHistory } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch)();\n    const { predictions, isLearning } = (0,_hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.usePredictiveNavigation)();\n    const contextualSuggestions = (0,_hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.useContextualSuggestions)();\n    // Enhanced prefetching with predictive navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            const allRoutes = navItems.map({\n                \"Sidebar.useEffect.allRoutes\": (item)=>item.href\n            }[\"Sidebar.useEffect.allRoutes\"]);\n            // Combine predictive routes with standard prefetching\n            const predictiveRoutes = predictions.slice(0, 2); // Top 2 predictions\n            const contextualRoutes = contextualSuggestions.filter({\n                \"Sidebar.useEffect.contextualRoutes\": (s)=>s.priority === 'high'\n            }[\"Sidebar.useEffect.contextualRoutes\"]).map({\n                \"Sidebar.useEffect.contextualRoutes\": (s)=>s.route\n            }[\"Sidebar.useEffect.contextualRoutes\"]).slice(0, 2);\n            const routesToPrefetch = [\n                ...predictiveRoutes,\n                ...contextualRoutes,\n                ...allRoutes.filter({\n                    \"Sidebar.useEffect.routesToPrefetch\": (route)=>route !== pathname && !predictiveRoutes.includes(route) && !contextualRoutes.includes(route)\n                }[\"Sidebar.useEffect.routesToPrefetch\"]),\n                '/playground',\n                '/logs'\n            ].slice(0, 6); // Increased limit for better coverage\n            console.log(\"\\uD83E\\uDDE0 [PREDICTIVE] Prefetching routes:\", {\n                predictive: predictiveRoutes,\n                contextual: contextualRoutes,\n                total: routesToPrefetch,\n                isLearning\n            });\n            const cleanup = prefetchWhenIdle(routesToPrefetch);\n            return cleanup;\n        }\n    }[\"Sidebar.useEffect\"], [\n        pathname,\n        prefetchWhenIdle,\n        predictions,\n        contextualSuggestions,\n        isLearning\n    ]);\n    // Determine if sidebar should be expanded (hover or not collapsed)\n    const isExpanded = !isCollapsed || isHovered;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"sidebar flex flex-col h-full flex-shrink-0 transition-all duration-200 ease-out bg-[#030614] relative \".concat(isExpanded ? 'w-64' : 'w-16'),\n        onMouseEnter: ()=>!isHoverDisabled && setHovered(true),\n        onMouseLeave: ()=>!isHoverDisabled && setHovered(false),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 right-0 h-full w-px bg-gradient-to-b from-gray-500/30 via-gray-400/40 to-gray-500/30\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 right-0 h-full w-0.5 bg-gray-400/15 blur-sm\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 transition-all duration-200 ease-out \".concat(isExpanded ? 'px-6' : 'px-3'),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8 pt-4 transition-all duration-200 ease-out \".concat(isExpanded ? '' : 'text-center'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"transition-all duration-200 ease-out \".concat(isExpanded ? 'opacity-0 scale-75 -translate-y-2' : 'opacity-100 scale-100 translate-y-0', \" \").concat(isExpanded ? 'absolute' : 'relative', \" w-8 h-8 bg-black rounded-lg flex items-center justify-center mx-auto p-0.5\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/RouKey_Logo_GLOW.png\",\n                                            alt: \"RouKey\",\n                                            width: 28,\n                                            height: 28,\n                                            className: \"object-contain\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"transition-all duration-200 ease-out \".concat(isExpanded ? 'opacity-100 scale-100 translate-y-0' : 'opacity-0 scale-75 translate-y-2', \" \").concat(isExpanded ? 'relative' : 'absolute top-0 left-0 w-full'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold text-white tracking-tight whitespace-nowrap\",\n                                                children: \"RouKey\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-400 mt-1 whitespace-nowrap\",\n                                                children: \"Smart LLM Router\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"space-y-2\",\n                            children: navItems.map((item)=>{\n                                const isActive = pathname === item.href || pathname.startsWith(item.href + '/');\n                                const Icon = isActive ? item.iconSolid : item.icon;\n                                const isPredicted = predictions.includes(item.href);\n                                const contextualSuggestion = contextualSuggestions.find((s)=>s.route === item.href);\n                                // Enhanced prefetch for playground to include chat history\n                                const handlePlaygroundHover = ()=>{\n                                    if (item.href === '/playground') {\n                                        // Prefetch route\n                                        prefetchOnHover(item.href, 50).onMouseEnter();\n                                        // Also prefetch chat history for current config if available\n                                        const currentConfigId = new URLSearchParams(window.location.search).get('config');\n                                        if (currentConfigId) {\n                                            prefetchChatHistory(currentConfigId);\n                                        }\n                                    }\n                                };\n                                const hoverProps = item.href === '/playground' ? {\n                                    onMouseEnter: handlePlaygroundHover\n                                } : prefetchOnHover(item.href, 50);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    onClick: (e)=>{\n                                        e.preventDefault();\n                                        navigateOptimistically(item.href);\n                                    },\n                                    className: \"sidebar-nav-item group flex items-center transition-all duration-200 ease-out w-full text-left \".concat(isActive ? 'active' : '', \" \").concat(isExpanded ? '' : 'collapsed'),\n                                    title: isExpanded ? undefined : item.label,\n                                    ...hoverProps,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex items-center w-full overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex items-center justify-center transition-all duration-200 ease-out \".concat(isExpanded ? 'w-5 h-5 mr-3' : 'w-10 h-10 rounded-xl', \" \").concat(!isExpanded && isActive ? 'bg-white shadow-sm' : !isExpanded ? 'bg-transparent hover:bg-white/10' : ''),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"transition-all duration-200 ease-out \".concat(isExpanded ? 'h-5 w-5' : 'h-5 w-5', \" \").concat(isActive ? 'text-orange-500' : 'text-white')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    isPredicted && !isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute rounded-full bg-blue-400 animate-pulse transition-all duration-200 ease-out \".concat(isExpanded ? '-top-1 -right-1 w-2 h-2' : '-top-1 -right-1 w-3 h-3'),\n                                                        title: \"Predicted next destination\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 transition-all duration-200 ease-out \".concat(isExpanded ? 'opacity-100 translate-x-0 max-w-full' : 'opacity-0 translate-x-4 max-w-0'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between whitespace-nowrap\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: item.label\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            contextualSuggestion && !isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs px-1.5 py-0.5 rounded-full ml-2 \".concat(contextualSuggestion.priority === 'high' ? 'bg-blue-500/20 text-blue-300' : 'bg-gray-500/20 text-gray-300'),\n                                                                children: contextualSuggestion.priority === 'high' ? '!' : '·'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs transition-colors duration-200 whitespace-nowrap \".concat(isActive ? 'text-orange-400' : 'text-gray-400'),\n                                                        children: contextualSuggestion ? contextualSuggestion.reason : item.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.href, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"4fmIjYR/bVR+uFRRDpXMUCW8BlQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_5__.useSidebar,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_6__.useNavigationSafe,\n        _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useRoutePrefetch,\n        _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useIntelligentPrefetch,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch,\n        _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.usePredictiveNavigation,\n        _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.useContextualSuggestions\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ })

});